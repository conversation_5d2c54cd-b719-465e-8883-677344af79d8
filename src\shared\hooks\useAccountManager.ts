/**
 * @file Hook for managing account operations and state
 */
import { useState, useCallback } from 'react';
import type { z } from 'zod';

import { ClipboardService } from '../../services/clipboardService';
import { useAccountStore } from '../store/accounts/accountStore';
import { useLogStore } from '../store/logStore';
import { accountSchema, type Account } from '../types/account';

// The form schema mirrors the main account schema but omits the ID
const formSchema = accountSchema.omit({ id: true });

export type AccountFormData = z.infer<typeof formSchema>;

interface DeletedAccountEntry {
  account: Account;
  deletedAt: number;
}

interface UseAccountManagerReturn {
  // State
  view: 'list' | 'form';
  setView: (_view: 'list' | 'form') => void;
  editingAccount: Account | null;
  setEditingAccount: (_account: Account | null) => void;
  error: string | null;
  setError: (_error: string | null) => void;
  prefillData: { email: string; password: string, refreshToken?: string, clientId?: string, isOAuth2?: boolean } | null;
  setPrefillData: (_data: { email: string; password: string, refreshToken?: string, clientId?: string, isOAuth2?: boolean } | null) => void;
  isImportDialogOpen: boolean;
  setIsImportDialogOpen: (_open: boolean) => void;
  deletedAccounts: DeletedAccountEntry[];
  setDeletedAccounts: (_accounts: DeletedAccountEntry[]) => void;

  // Handlers
  handleSave: (_data: AccountFormData) => Promise<void>;
  handleAddNew: () => Promise<void>;
  handleEdit: (_account: Account) => void;
  handleDelete: (_accountId: string) => Promise<void>;
  handleCancel: () => void;
  handleCopyCredentials: (_account: Account) => Promise<void>;
  handleImport: () => void;
  handleImportComplete: (_result: { addedCount: number; skippedCount: number }) => void;
  handleUndoDelete: (_accountId: string) => Promise<void>;
  handleDismissUndo: (_accountId: string) => void;
  handleClearAllUndo: () => void;
}

/**
 * Hook for managing account operations
 */
export const useAccountManager = (): UseAccountManagerReturn => {
  const {
    accounts,
    addAccountToStore,
    updateAccountInStore,
    deleteAccountInStore,
    setAccounts
  } = useAccountStore();
  
  const addLog = useLogStore((state) => state.addLog);
  
  const [view, setView] = useState<'list' | 'form'>('list');
  const [editingAccount, setEditingAccount] = useState<Account | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [prefillData, setPrefillData] = useState<{ email: string; password: string, refreshToken?: string, clientId?: string, isOAuth2?: boolean } | null>(null);
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false);
  const [deletedAccounts, setDeletedAccounts] = useState<DeletedAccountEntry[]>([]);

  const handleSave = useCallback(async (data: AccountFormData) => {
    console.log('handleSave called with data:', data);
    console.log('handleSave incoming data:', JSON.stringify(data.incoming, null, 2));
    setError(null);
    const dataToSave = { ...data };

    try {
      if (editingAccount) {
        console.log('Updating existing account');
        const updatedAccount = await window.ipcApi.updateAccount(editingAccount.id, dataToSave);
        addLog(`Account "${(updatedAccount.displayName?.length ?? 0) > 0 ? updatedAccount.displayName : updatedAccount.email}" updated.`, 'success');
      } else {
        console.log('Adding new account');
        if ((dataToSave.displayName?.length ?? 0) === 0) {
          const emailDomain = dataToSave.email.split('@')[1]?.split('.')[0] || 'Email';
          const newAccountName = `Account ${accounts.length + 1} ${emailDomain.toUpperCase()}`;
          dataToSave.displayName = newAccountName;
        }

        console.log('Calling window.ipcApi.addAccount with:', dataToSave);
        const newAccount = await window.ipcApi.addAccount(dataToSave);
        console.log('Account added successfully:', newAccount);
        addAccountToStore(newAccount);
        addLog(`Account "${newAccount.displayName}" added.`, 'success');
      }

      // ALWAYS reload all accounts from file to ensure store is in sync.
      const allAccounts = await window.ipcApi.getAccounts();
      setAccounts(allAccounts);

      setView('list');
      setEditingAccount(null);
    } catch (err: unknown) {
      console.error('Error in handleSave:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to save account.';
      setError(errorMessage);
      addLog(errorMessage, 'error');
    }
  }, [editingAccount, accounts.length, addAccountToStore, addLog, setAccounts]);

  const handleAddNew = useCallback(async () => {
    console.log('handleAddNew called');
    let prefill: { email: string; password: string, refreshToken?: string, clientId?: string, isOAuth2?: boolean } | null = null;

    try {
      const result = await ClipboardService.detectCredentialsFromClipboard();

      if (result.success && result.credentials) {
        prefill = result.credentials;
        addLog('Credentials detected in clipboard, pre-filling form.', 'info');
      } else if ((result.error?.includes('resembles credentials')) === true) {
        addLog(result.error, 'info');
      }
    } catch (_err) {
      // eslint-disable-next-line no-console
      console.log('Could not read clipboard content.');
      // Don't block the UI if clipboard reading fails
    }

    // Use setTimeout to prevent UI blocking
    setTimeout(() => {
      console.log('Setting view to form with prefill:', prefill);
      setPrefillData(prefill);
      setEditingAccount(null);
      setView('form');
    }, 0);
  }, [addLog]);

  const handleEdit = useCallback((account: Account) => {
    setEditingAccount(account);
    setView('form');
  }, []);

  const handleDelete = useCallback(async (accountId: string) => {
    const accountToDelete = accounts.find(acc => acc.id === accountId);
    if (!accountToDelete) return;

    const accountName = (accountToDelete.displayName?.length ?? 0) > 0 ? accountToDelete.displayName : accountToDelete.email;

    try {
      await window.ipcApi.deleteAccount(accountId);
      deleteAccountInStore(accountId);

      // Add to deleted accounts stack (max 10 accounts)
      setDeletedAccounts(prev => {
        const newEntry: DeletedAccountEntry = {
          account: accountToDelete,
          deletedAt: Date.now()
        };

        // Keep only last 10 deleted accounts
        const updated = [newEntry, ...prev].slice(0, 10);

        // Auto-cleanup entries older than 5 minutes
        return updated.filter(entry => Date.now() - entry.deletedAt < 5 * 60 * 1000);
      });

      addLog(`Account "${accountName}" deleted.`, 'success');
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete account.';
      setError(errorMessage);
      addLog(errorMessage, 'error');
    }
  }, [accounts, deleteAccountInStore, addLog]);

  const handleCancel = useCallback(() => {
    setEditingAccount(null);
    setView('list');
    setError(null);
  }, []);

  const handleCopyCredentials = useCallback(async (account: Account) => {
    try {
      const success = await ClipboardService.copyAccountCredentials(account.email, account.password);
      if (success) {
        addLog(`Credentials for "${(account.displayName?.length ?? 0) > 0 ? account.displayName : account.email}" copied to clipboard.`, 'success');
      } else {
        addLog('Failed to copy credentials to clipboard.', 'error');
      }
    } catch (_error) {
      addLog('Failed to copy credentials to clipboard.', 'error');
    }
  }, [addLog]);

  const handleImport = useCallback(() => {
    setIsImportDialogOpen(true);
  }, []);

  const handleImportComplete = useCallback((result: { addedCount: number; skippedCount: number }) => {
    addLog(`Successfully imported ${result.addedCount} accounts. ${result.skippedCount} lines were skipped.`, 'success');

    // Refresh accounts from store asynchronously
    void Promise.resolve(window.ipcApi.getAccounts()).then((updatedAccounts: Account[]) => {
      setAccounts(updatedAccounts);
    }).catch((error: unknown) => {
      const errorMessage = error instanceof Error ? error.message : 'Failed to refresh accounts.';
      addLog(errorMessage, 'error');
    });

    // Close the dialog
    setIsImportDialogOpen(false);
  }, [addLog, setAccounts]);

  const handleUndoDelete = useCallback(async (accountId: string) => {
    const deletedEntry = deletedAccounts.find(entry => entry.account.id === accountId);
    if (!deletedEntry) return;

    try {
      const restoredAccount = await window.ipcApi.addAccount(deletedEntry.account);
      addAccountToStore(restoredAccount);

      // Remove from deleted accounts stack
      setDeletedAccounts(prev => prev.filter(entry => entry.account.id !== accountId));

      addLog(`Account "${(deletedEntry.account.displayName?.length ?? 0) > 0 ? deletedEntry.account.displayName : deletedEntry.account.email}" restored.`, 'success');
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to restore account.';
      setError(errorMessage);
      addLog(errorMessage, 'error');
    }
  }, [deletedAccounts, addAccountToStore, addLog]);

  const handleDismissUndo = useCallback((accountId: string) => {
    // Remove specific account from deleted stack (user explicitly dismissed)
    setDeletedAccounts(prev => prev.filter(entry => entry.account.id !== accountId));
  }, []);

  const handleClearAllUndo = useCallback(() => {
    // Clear all deleted accounts (e.g., on app close)
    setDeletedAccounts([]);
  }, []);

  return {
    view,
    setView,
    editingAccount,
    setEditingAccount,
    error,
    setError,
    prefillData,
    setPrefillData,
    isImportDialogOpen,
    setIsImportDialogOpen,
    deletedAccounts,
    setDeletedAccounts,
    handleSave,
    handleAddNew,
    handleEdit,
    handleDelete,
    handleCancel,
    handleCopyCredentials,
    handleImport,
    handleImportComplete,
    handleUndoDelete,
    handleDismissUndo,
    handleClearAllUndo,
  };
};
