import axios, { AxiosError } from 'axios';
import { URLSearchParams } from 'url';

export interface TokenResponse {
  access_token: string;
  refresh_token?: string;
  expires_in: number;
  scope: string;
  token_type: string;
}

export interface MsalError {
  error: string;
  error_description: string;
  error_codes?: number[];
  timestamp?: string;
  trace_id?: string;
  correlation_id?: string;
}

export class MsalService {
  private static readonly TOKEN_URL = "https://login.microsoftonline.com/common/oauth2/v2.0/token";
  private static readonly DEFAULT_SCOPE = 'https://outlook.office.com/IMAP.AccessAsUser.All offline_access';
  private static readonly RETRY_ATTEMPTS = 2;
  private static readonly RETRY_DELAY = 1000; // 1 second

  /**
   * Gets access token using refresh token with improved error handling and retry logic
   */
  static async getAccessToken(
    clientId: string,
    refreshToken: string,
    proxy?: string,
    scope?: string
  ): Promise<TokenResponse> {
    const params = new URLSearchParams();
    params.append('client_id', clientId);
    params.append('refresh_token', refreshToken);
    params.append('grant_type', 'refresh_token');
    params.append('scope', scope || this.DEFAULT_SCOPE);

    const config = {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      timeout: 30000, // 30 seconds timeout
    };

    let lastError: Error | null = null;

    // Attempt with proxy first (if provided)
    if (proxy) {
      try {
        const response = await axios.post(this.TOKEN_URL, params, config);
        return this.validateTokenResponse(response.data);
      } catch (error) {
        lastError = this.handleTokenError(error, 'with proxy');
        console.warn('Token request failed with proxy, trying without proxy:', lastError.message);
      }
    }

    // Attempt without proxy with retry logic
    for (let attempt = 1; attempt <= this.RETRY_ATTEMPTS; attempt++) {
      try {
        const response = await axios.post(this.TOKEN_URL, params, {
          headers: config.headers,
          timeout: config.timeout
        });
        return this.validateTokenResponse(response.data);
      } catch (error) {
        lastError = this.handleTokenError(error, `attempt ${attempt}`);

        if (attempt < this.RETRY_ATTEMPTS) {
          console.warn(`Token request failed on attempt ${attempt}, retrying in ${this.RETRY_DELAY}ms...`);
          await this.delay(this.RETRY_DELAY);
        }
      }
    }

    // If all attempts failed, throw the last error
    throw lastError || new Error('All token acquisition attempts failed');
  }

  /**
   * Validates and normalizes token response
   */
  private static validateTokenResponse(data: any): TokenResponse {
    if (!data || typeof data !== 'object') {
      throw new Error('Invalid token response format');
    }

    if (!data.access_token) {
      throw new Error('Access token not found in response');
    }

    return {
      access_token: data.access_token,
      refresh_token: data.refresh_token,
      expires_in: data.expires_in || 3600,
      scope: data.scope || this.DEFAULT_SCOPE,
      token_type: data.token_type || 'Bearer',
    };
  }

  /**
   * Handles and categorizes token acquisition errors
   */
  private static handleTokenError(error: unknown, context: string): Error {
    if (axios.isAxiosError(error)) {
      const axiosError = error as AxiosError<MsalError>;

      if (axiosError.response?.data) {
        const msalError = axiosError.response.data;
        const errorMsg = `Microsoft OAuth2 error (${context}): ${msalError.error} - ${msalError.error_description}`;

        // Categorize specific errors
        if (msalError.error === 'invalid_grant') {
          return new Error(`${errorMsg}. The refresh token may be expired or revoked. Please re-authenticate.`);
        } else if (msalError.error === 'invalid_client') {
          return new Error(`${errorMsg}. The client ID may be invalid or unauthorized.`);
        } else if (msalError.error === 'unauthorized_client') {
          return new Error(`${errorMsg}. The client is not authorized for this operation.`);
        }

        return new Error(errorMsg);
      } else if (axiosError.code === 'ECONNABORTED') {
        return new Error(`Token request timeout (${context}). Please check your internet connection.`);
      } else if (axiosError.code === 'ENOTFOUND' || axiosError.code === 'ECONNREFUSED') {
        return new Error(`Network error (${context}): ${axiosError.message}`);
      }

      return new Error(`HTTP error (${context}): ${axiosError.message}`);
    }

    return new Error(`Unknown error (${context}): ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  /**
   * Simple delay utility for retry logic
   */
  private static delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Generates OAuth2 authentication string for IMAP XOAUTH2
   */
  static generateAuthString(email: string, accessToken: string): string {
    return `user=${email}\x01auth=Bearer ${accessToken}\x01\x01`;
  }

  /**
   * Checks if an error indicates the need for re-authentication
   */
  static isReauthenticationRequired(error: Error): boolean {
    const message = error.message.toLowerCase();
    return message.includes('invalid_grant') ||
           message.includes('expired') ||
           message.includes('revoked') ||
           message.includes('unauthorized');
  }
}