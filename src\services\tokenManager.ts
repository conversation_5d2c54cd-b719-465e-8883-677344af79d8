/**
 * @file Centralized OAuth2 token management with caching and automatic refresh
 * Prevents multiple authentication requests for the same account
 */

import { MsalService } from './msalService';

interface CachedToken {
  accessToken: string;
  expiresAt: number;
  refreshToken: string;
  clientId: string;
}

interface TokenRequest {
  clientId: string;
  refreshToken: string;
  proxy?: string;
  scope?: string;
}

/**
 * Centralized manager for OAuth2 access tokens with intelligent caching
 */
export class TokenManager {
  private static instance: TokenManager;
  private tokenCache = new Map<string, CachedToken>();
  private pendingRequests = new Map<string, Promise<string>>();
  
  // Buffer time before token expiry to refresh proactively (5 minutes)
  private static readonly REFRESH_BUFFER_MS = 5 * 60 * 1000;

  private constructor() {}

  public static getInstance(): TokenManager {
    if (!TokenManager.instance) {
      TokenManager.instance = new TokenManager();
    }
    return TokenManager.instance;
  }

  /**
   * Get access token with intelligent caching and deduplication
   */
  public async getAccessToken(request: TokenRequest): Promise<string> {
    const cacheKey = this.getCacheKey(request.clientId, request.refreshToken);
    
    // Check if we have a valid cached token
    const cachedToken = this.tokenCache.get(cacheKey);
    if (cachedToken && this.isTokenValid(cachedToken)) {
      console.log(`[TokenManager] Using cached token for ${request.clientId} (expires in ${Math.round((cachedToken.expiresAt - Date.now()) / 60000)} minutes)`);
      return cachedToken.accessToken;
    }

    // Check if there's already a pending request for this token
    const pendingRequest = this.pendingRequests.get(cacheKey);
    if (pendingRequest) {
      console.log(`[TokenManager] Waiting for pending token request for ${request.clientId}`);
      return await pendingRequest;
    }

    // Create new token request
    const tokenPromise = this.fetchNewToken(request, cacheKey);
    this.pendingRequests.set(cacheKey, tokenPromise);

    try {
      const accessToken = await tokenPromise;
      return accessToken;
    } finally {
      // Always clean up pending request
      this.pendingRequests.delete(cacheKey);
    }
  }

  /**
   * Fetch new token from MSAL service and cache it
   */
  private async fetchNewToken(request: TokenRequest, cacheKey: string): Promise<string> {
    console.log(`[TokenManager] Fetching new token for ${request.clientId}`);
    
    try {
      const tokenResponse = await MsalService.getAccessToken(
        request.clientId,
        request.refreshToken,
        request.proxy,
        request.scope
      );

      // Cache the token with expiry information
      const expiresAt = Date.now() + (tokenResponse.expires_in * 1000);
      this.tokenCache.set(cacheKey, {
        accessToken: tokenResponse.access_token,
        expiresAt,
        refreshToken: request.refreshToken,
        clientId: request.clientId,
      });

      console.log(`[TokenManager] Token cached for ${request.clientId}, expires at ${new Date(expiresAt).toISOString()}`);
      return tokenResponse.access_token;
    } catch (error) {
      console.error(`[TokenManager] Failed to fetch token for ${request.clientId}:`, error);
      // Remove any stale cached token on error
      this.tokenCache.delete(cacheKey);
      throw error;
    }
  }

  /**
   * Check if cached token is still valid (not expired with buffer)
   */
  private isTokenValid(cachedToken: CachedToken): boolean {
    const now = Date.now();
    const isValid = now < (cachedToken.expiresAt - TokenManager.REFRESH_BUFFER_MS);
    
    if (!isValid) {
      console.log(`[TokenManager] Token expired or near expiry for ${cachedToken.clientId}`);
    }
    
    return isValid;
  }

  /**
   * Generate cache key for token storage
   */
  private getCacheKey(clientId: string, refreshToken: string): string {
    // Use first 8 chars of refresh token for uniqueness while keeping key manageable
    const tokenHash = refreshToken.substring(0, 8);
    return `${clientId}:${tokenHash}`;
  }

  /**
   * Generate OAuth2 authentication string for IMAP
   */
  public generateAuthString(email: string, accessToken: string): string {
    return MsalService.generateAuthString(email, accessToken);
  }

  /**
   * Invalidate cached token for specific account
   */
  public invalidateToken(clientId: string, refreshToken: string): void {
    const cacheKey = this.getCacheKey(clientId, refreshToken);
    this.tokenCache.delete(cacheKey);
    console.log(`[TokenManager] Invalidated token for ${clientId}`);
  }

  /**
   * Clear all cached tokens (useful for logout or reset)
   */
  public clearAllTokens(): void {
    this.tokenCache.clear();
    this.pendingRequests.clear();
    console.log('[TokenManager] All tokens cleared');
  }

  /**
   * Get cache statistics for debugging
   */
  public getCacheStats(): { cachedTokens: number; pendingRequests: number } {
    return {
      cachedTokens: this.tokenCache.size,
      pendingRequests: this.pendingRequests.size,
    };
  }
}

// Export singleton instance
export const tokenManager = TokenManager.getInstance();
