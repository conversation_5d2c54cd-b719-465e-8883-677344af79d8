@echo off
title ImapViewer Builder
echo Installing dependencies using npm...
call npm install
if %errorlevel% neq 0 (
    echo Error: npm install failed.
    pause
    exit /b %errorlevel%
)
echo.
echo Building the application...
call npm run make
if %errorlevel% neq 0 (
    echo Error: npm run make failed.
    pause
    exit /b %errorlevel%
)
echo.
echo Build process finished. Check the 'out' directory.
pause 