/**
 * @file Manages active IMAP connections for user accounts using ImapFlow.
 */
import type { ImapFlow } from 'imapflow';

const activeConnections = new Map<string, ImapFlow>();
const pendingConnections = new Map<string, Promise<ImapFlow>>();

export const imapFlowConnectionManager = {
  get(accountId: string): ImapFlow | undefined {
    const imap = activeConnections.get(accountId);
    // Проверка на активное соединение
    if (imap?.usable === true) {
      return imap;
    }
    // Если соединение неактивно, удаляем его
    if (imap) {
      activeConnections.delete(accountId);
    }
    return undefined;
  },

  /**
   * Get existing connection or wait for pending connection
   */
  async getOrWaitForConnection(accountId: string): Promise<ImapFlow | undefined> {
    // Check for existing active connection
    const existingConnection = this.get(accountId);
    if (existingConnection) {
      return existingConnection;
    }

    // Check for pending connection
    const pendingConnection = pendingConnections.get(accountId);
    if (pendingConnection) {
      console.log(`[ConnectionManager] Waiting for pending connection for ${accountId}`);
      try {
        return await pendingConnection;
      } catch (error) {
        console.error(`[ConnectionManager] Pending connection failed for ${accountId}:`, error);
        return undefined;
      }
    }

    return undefined;
  },

  /**
   * Set a pending connection promise to prevent duplicate connection attempts
   */
  setPendingConnection(accountId: string, connectionPromise: Promise<ImapFlow>): void {
    pendingConnections.set(accountId, connectionPromise);

    // Clean up pending connection when it resolves or rejects
    connectionPromise
      .then((imap) => {
        pendingConnections.delete(accountId);
        this.set(accountId, imap);
      })
      .catch(() => {
        pendingConnections.delete(accountId);
      });
  },

  /**
   * Check if there's a pending connection for this account
   */
  hasPendingConnection(accountId: string): boolean {
    return pendingConnections.has(accountId);
  },

  set(accountId: string, imap: ImapFlow): void {
    const existingConnection = activeConnections.get(accountId);
    if (existingConnection && existingConnection !== imap && existingConnection.usable) {
      existingConnection.logout().catch(err => {
        // eslint-disable-next-line no-console
        console.error(`Error logging out existing connection for ${accountId}:`, err);
        existingConnection.close();
      });
    }

    activeConnections.set(accountId, imap);
    
    // Добавляем обработчик ошибок
    imap.on('error', (err) => {
      // eslint-disable-next-line no-console
      console.error(`IMAP error for account ${accountId}:`, err);
    });

    // Обработчик закрытия соединения
    imap.on('close', () => {
      // eslint-disable-next-line no-console
      console.log(`IMAP connection closed for account ${accountId}`);
      activeConnections.delete(accountId);
    });
  },

  async end(accountId: string): Promise<void> {
    const imap = activeConnections.get(accountId);
    if (imap) {
      try {
        // Корректное закрытие сессии
        if (imap.usable) {
          await imap.logout();
        } else {
          imap.close();
        }
      } catch (err) {
        // eslint-disable-next-line no-console
        console.error(`Error closing connection for account ${accountId}:`, err);
        // В случае ошибки принудительно закрываем соединение
        imap.close();
      }
      activeConnections.delete(accountId);
      // eslint-disable-next-line no-console
      console.log(`Closed connection for account ${accountId}`);
    }
  },

  async endAll(): Promise<void> {
    // Создаем массив промисов для закрытия всех соединений
    const closePromises = Array.from(activeConnections.entries()).map(
      async ([accountId, imap]) => {
        try {
          // eslint-disable-next-line no-console
          console.log(`Closing connection for ${accountId}`);
          if (imap.usable) {
            await imap.logout();
          } else {
            imap.close();
          }
        } catch (err) {
          // eslint-disable-next-line no-console
          console.error(`Error closing connection for ${accountId}:`, err);
          imap.close();
        }
      }
    );

    // Ждем завершения всех закрытий
    await Promise.allSettled(closePromises);
    activeConnections.clear();
    pendingConnections.clear();
    // eslint-disable-next-line no-console
    console.log('All active IMAP connections have been closed.');
  },

  /**
   * Get connection statistics for debugging
   */
  getStats(): { activeConnections: number; pendingConnections: number } {
    return {
      activeConnections: activeConnections.size,
      pendingConnections: pendingConnections.size,
    };
  }
}; 