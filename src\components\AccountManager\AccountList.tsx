/**
 * @file Account list component for displaying and managing accounts
 */
import { Co<PERSON>, Edit, Trash2, LogIn } from 'lucide-react';
import React from 'react';

import type { Account } from '../../shared/types/account';
import { useMainSettingsStore } from '../../shared/store/mainSettingsStore';
import { Avatar, AvatarFallback } from '../../shared/ui';
import { Button } from '../../shared/ui/button';
import { cn } from '../../shared/utils/utils';

interface AccountListProps {
  accounts: Account[];
  selectedAccountId: string | null;
  recentlyDeletedId: string | null;
  onSelectAccount: (accountId: string) => void;
  onContextMenu: (e: React.MouseEvent, accountId: string) => void;
  onEdit: (account: Account) => void;
  onDelete: (accountId: string) => void;
  onUndo: (accountId: string) => void;
  onCopyCredentials: (account: Account) => void;
  onConnectAccount?: (accountId: string) => void;
  collapsed?: boolean;
}

export const AccountList: React.FC<AccountListProps> = ({
  accounts,
  selectedAccountId,
  recentlyDeletedId,
  onSelectAccount,
  onContextMenu,
  onEdit,
  onDelete,
  onUndo,
  onCopyCredentials,
  onConnectAccount,
  collapsed = false,
}) => {
  const { settings } = useMainSettingsStore();

  if (accounts.length === 0) {
    return (
      <div className="flex-1 flex items-center justify-center p-4">
        <div className="text-center text-muted-foreground">
          <p className="text-sm">No accounts configured</p>
          {!collapsed && (
            <p className="text-xs mt-1">Add an account to get started</p>
          )}
        </div>
      </div>
    );
  }

  // Collapsed sidebar view - show only numbered buttons
  if (collapsed) {
    return (
      <nav className={cn(
        "flex flex-col items-center w-full px-2",
        settings.compactAccountView === true ? "gap-2" : "gap-3"
      )} aria-label="Account list">
        {accounts.map((account, index) => (
          <div key={account.id} className="relative group">
            <button
              onClick={() => onSelectAccount(account.id)}
              title={`${account.displayName ?? account.email} - ${account.connectionStatus ?? 'disconnected'}`}
              className={cn(
                "flex items-center justify-center text-xs font-mono focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all",
                settings.compactAccountView === true ? "w-8 h-6 rounded-lg" : "w-8 h-8 rounded-full",
                selectedAccountId === account.id ? "ring-2 ring-primary" : "",
                account.connectionStatus === 'connected' ? "bg-green-500/20 text-green-400" :
                account.connectionStatus === 'connecting' ? "bg-yellow-500/20 text-yellow-400" :
                "bg-red-500/20 text-red-400"
              )}
              aria-label={`Select account ${account.displayName ?? account.email}`}
            >
              {index + 1}
            </button>

            {/* Login hover button for collapsed view */}
            {onConnectAccount && (
              <div className="absolute -right-8 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => onConnectAccount(account.id)}
                  className="h-6 w-6 bg-card/95 backdrop-blur-sm border border-border shadow-lg hover:bg-accent/50"
                  title="Connect to account"
                >
                  <LogIn size={12} />
                </Button>
              </div>
            )}
          </div>
        ))}
      </nav>
    );
  }

  return (
    <div className="flex-1 overflow-y-auto py-2 px-3">
      <div className="space-y-1">
        {accounts.map((acc, index) => {
          const displayLabel = acc.displayName ?? acc.email;
          const isSelected = selectedAccountId === acc.id;

          const isDefaultName = /^Account \d+ \w+$/.test(acc.displayName ?? '');
          const avatarName = isDefaultName ? acc.email : acc.displayName;

          return (
            <div
              key={acc.id}
              className={`
                relative rounded-lg transition-all group overflow-hidden
                ${isSelected ? 'bg-blue-900/30' : 'hover:bg-white/5'}
              `}
              onDoubleClick={() => onEdit(acc)}
              onContextMenu={(e) => onContextMenu(e, acc.id)}
            >
              <div className={cn("flex items-center", settings.compactAccountView === true ? "p-1.5" : "p-2")}>
                <div
                  className="flex items-center flex-grow gap-3 min-w-0 text-left cursor-pointer"
                  onClick={() => onSelectAccount(acc.id)}
                  title={acc.email}
                >
                  {settings.compactAccountView === true ? (
                    <div className="flex items-center gap-2 min-w-0 flex-1">
                      <span
                        className={cn(
                          "text-xs font-mono w-5 h-5 rounded-full flex items-center justify-center flex-shrink-0",
                          acc.connectionStatus === 'connected' ? "bg-green-500/20 text-green-400" :
                          acc.connectionStatus === 'connecting' ? "bg-yellow-500/20 text-yellow-400" :
                          "bg-red-500/20 text-red-400"
                        )}
                        title={`Account ${index + 1} - ${acc.connectionStatus ?? 'disconnected'}`}
                      >
                        {index + 1}
                      </span>
                      <span className="text-sm truncate">
                        {acc.displayName ?? acc.email}
                      </span>
                    </div>
                  ) : (
                    <>
                      <div className="relative group/avatar">
                        <Avatar className={cn(
                          "w-10 h-10",
                          isSelected && "ring-2 ring-primary"
                        )}>
                          <AvatarFallback className={cn(
                            "font-medium text-sm",
                            isSelected ? "bg-primary text-primary-foreground" : "bg-muted"
                          )}>
                            {(avatarName?.length ?? 0) > 0 ?
                              (avatarName ?? '').split(' ').map(part => part[0]).slice(0, 2).join('').toUpperCase() :
                              acc.email[0]?.toUpperCase() ?? '?'
                            }
                          </AvatarFallback>
                        </Avatar>

                        {/* Login hover button for avatar */}
                        {onConnectAccount && (
                          <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover/avatar:opacity-100 transition-opacity duration-200 bg-black/50 rounded-full">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={(e) => {
                                e.stopPropagation();
                                onConnectAccount(acc.id);
                              }}
                              className="h-6 w-6 bg-card/95 backdrop-blur-sm border border-border shadow-lg hover:bg-accent/50"
                              title="Connect to account"
                            >
                              <LogIn size={12} />
                            </Button>
                          </div>
                        )}

                        {/* Index number */}
                        <div className="absolute -top-1 -left-1 rounded-full border-2 border-background bg-muted text-muted-foreground text-xs font-bold flex items-center justify-center w-5 h-5">
                          {index + 1}
                        </div>

                        {/* Status indicator */}
                        {acc.connectionStatus && (
                          <div className={cn(
                            "absolute -bottom-1 -right-1 rounded-full border-2 border-background w-3 h-3",
                            acc.connectionStatus === 'connected' && 'bg-green-500',
                            acc.connectionStatus === 'connecting' && 'bg-yellow-500',
                            acc.connectionStatus === 'disconnected' && 'bg-red-500'
                          )} />
                        )}
                      </div>

                      <div className="flex-grow min-w-0 overflow-hidden">
                        <p className={`truncate text-sm ${isSelected ? 'text-blue-200 font-medium' : 'text-gray-200'}`}>
                          {displayLabel}
                        </p>
                        <p className="truncate text-xs text-gray-400">{acc.email}</p>
                      </div>
                    </>
                  )}
                </div>
              </div>

              <div className={cn(
                "absolute inset-y-0 right-0 flex items-center opacity-0 transition-opacity duration-75",
                recentlyDeletedId !== acc.id && "group-hover:opacity-100"
              )}>
                <div className="flex h-full items-center gap-0.5 bg-card/95 backdrop-blur-sm px-2 py-1 rounded-l-full border-l border-t border-b border-border shadow-lg">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-7 w-7 rounded-full hover:bg-accent/50"
                    onClick={() => { void onCopyCredentials(acc); }}
                    title="Copy credentials"
                  >
                    <Copy size={12} />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-7 w-7 rounded-full hover:bg-accent/50"
                    onClick={() => onEdit(acc)}
                    title="Edit account"
                  >
                    <Edit size={12} />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-7 w-7 rounded-full text-destructive hover:text-destructive hover:bg-destructive/10"
                    onClick={() => { void onDelete(acc.id); }}
                    title="Delete account"
                  >
                    <Trash2 size={12} />
                  </Button>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};
