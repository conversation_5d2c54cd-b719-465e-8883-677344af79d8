"use strict";
/**
 * Test script to verify the GMX email discovery fix
 */
Object.defineProperty(exports, "__esModule", { value: true });
const dnsDiscovery_1 = require("./src/services/discovery/dnsDiscovery");
// Simple logger function
const logger = (level, message) => {
    console.log(`[${level.toUpperCase()}] ${message}`);
};
async function testGmxDiscovery() {
    console.log('Testing GMX email discovery...');
    console.log('=====================================');
    const domain = 'gmx.ch';
    try {
        // Test DNS discovery directly
        const config = await (0, dnsDiscovery_1.discoverViaDns)(domain, logger);
        if (config) {
            console.log('\n✅ Discovery successful!');
            console.log('Configuration found:');
            console.log('==================');
            if (config.imap) {
                console.log(`IMAP: ${config.imap.host}:${config.imap.port} (secure: ${config.imap.secure})`);
            }
            if (config.smtp) {
                console.log(`SMTP: ${config.smtp.host}:${config.smtp.port} (secure: ${config.smtp.secure})`);
            }
            // Check for hostname consistency
            if (config.imap && config.smtp) {
                const imapDomain = config.imap.host.split('.').slice(-2).join('.');
                const smtpDomain = config.smtp.host.split('.').slice(-2).join('.');
                console.log('\n🔍 Hostname consistency check:');
                console.log(`IMAP domain: ${imapDomain}`);
                console.log(`SMTP domain: ${smtpDomain}`);
                if (imapDomain === smtpDomain) {
                    console.log('✅ Hostnames are consistent!');
                    console.log('🎉 Fix is working correctly!');
                }
                else {
                    console.log('❌ Hostname mismatch detected!');
                    console.log('This indicates the fix may not be working correctly.');
                }
                // Additional check: both should use the same base hostname
                const imapHost = config.imap.host;
                const smtpHost = config.smtp.host;
                console.log('\n🔍 Detailed hostname analysis:');
                console.log(`IMAP host: ${imapHost}`);
                console.log(`SMTP host: ${smtpHost}`);
                // Check if both use mail.gmx.net (the expected result after fix)
                if (imapHost.includes('mail.gmx.net') && smtpHost.includes('mail.gmx.net')) {
                    console.log('✅ Both servers correctly use mail.gmx.net!');
                }
                else if (imapHost.includes('mail.gmx.net') && smtpHost.includes('mail.gmx.ch')) {
                    console.log('❌ SMTP still uses mail.gmx.ch instead of mail.gmx.net');
                    console.log('This is the exact issue we are trying to fix.');
                }
            }
        }
        else {
            console.log('❌ Discovery failed - no configuration found');
        }
    }
    catch (error) {
        console.error('❌ Discovery error:', error instanceof Error ? error.message : error);
    }
}
// Run the test
testGmxDiscovery().catch(console.error);
