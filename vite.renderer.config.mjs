import { defineConfig } from 'vite';
import { resolve } from 'path';
import react from '@vitejs/plugin-react';
import tailwindcss from '@tailwindcss/vite';

// https://vitejs.dev/config
export default defineConfig({
  resolve: {
    alias: {
      '@': resolve('src'),
    },
  },
  plugins: [react(), tailwindcss()],
  server: {
    port: 4100,
    watch: {
      // Ignore config.json to prevent reload loops
      ignored: ['**/config.json', '**/accounts.txt', '**/domains.txt', '**/proxies.txt']
    }
  }
});
