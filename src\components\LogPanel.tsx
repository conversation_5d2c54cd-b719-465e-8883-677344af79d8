/**
 * @file A persistent sidebar panel for displaying application logs.
 */
import {
  Trash2, Terminal,
  RefreshCw, ChevronUp, ChevronDown
} from 'lucide-react';
import React from 'react';

import { useProxyStatus } from '../shared/hooks/useProxyStatus';
import { useLogStore, type LogType } from '../shared/store/logStore';
import { useUIStore } from '../shared/store/uiStore';
import { Button } from '../shared/ui/button';

const logColors: Record<LogType | 'default', { text: string, dot: string }> = {
  success: {
    text: 'text-green-400',
    dot: 'bg-green-500'
  },
  error: {
    text: 'text-destructive',
    dot: 'bg-destructive'
  },
  info: {
    text: 'text-primary',
    dot: 'bg-primary'
  },
  warn: {
    text: 'text-yellow-400',
    dot: 'bg-yellow-500'
  },
  default: {
    text: 'text-gray-400',
    dot: 'bg-gray-500'
  }
};

const ProxyStatusIndicator = (): React.JSX.Element => {
  const {
    status,
    statusInfo,
    label,
    tooltip,
    textColor,
    handleRefresh,
  } = useProxyStatus();

  const { Icon, color } = statusInfo;

  return (
    <div className="flex items-center gap-1.5 group" title={tooltip}>
      <div className="h-2 w-px bg-gray-700" />
      <Icon size={14} className={`${color} ${status === 'connecting' ? 'animate-spin' : ''}`} />
      <span className={`text-xs font-medium ${textColor} truncate max-w-[180px]`}>{label}</span>
      
      {status !== 'disabled' && (
        <button 
          onClick={handleRefresh} 
          disabled={status === 'connecting'}
          className="p-0.5 rounded-full text-gray-400 hover:text-white hover:bg-white/10 disabled:opacity-50 disabled:cursor-not-allowed opacity-0 group-hover:opacity-100 transition-opacity"
          title="Re-check proxy status"
        >
          <RefreshCw size={12} />
        </button>
      )}
    </div>
  );
};

/**
 * Compact time formatter - returns only hours:minutes:seconds
 */
const formatTime = (timestamp: number): string => {
  const date = new Date(timestamp);
  return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: false });
};

/**
 * Log panel component that displays application events and messages in a compact format
 */
const LogPanel = (): React.JSX.Element => {
  const { logs, clearLogs } = useLogStore();
  const { isLogPanelCollapsed, toggleLogPanel } = useUIStore();
  const scrollRef = React.useRef<HTMLDivElement>(null);

  const filteredLogs = logs.filter(
    (log) => !log.message.startsWith('[IMAP')
  );

  React.useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [logs]);

  return (
    <div className="h-full flex flex-col bg-background text-foreground w-full">
      <div className="flex items-center justify-between py-1 px-2 border-b border-border">
        <div className="flex items-center gap-1.5">
          <Terminal size={14} className="text-muted-foreground" />
          <h3 className="text-xs font-medium">Event Log</h3>
          <ProxyStatusIndicator />
        </div>
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleLogPanel}
            title={isLogPanelCollapsed ? "Expand log panel" : "Collapse log panel"}
            className="h-6 w-6 rounded-full text-muted-foreground hover:text-foreground hover:bg-muted/50"
          >
            {isLogPanelCollapsed ? <ChevronUp size={14} /> : <ChevronDown size={14} />}
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={clearLogs}
            title="Clear logs"
            className="h-6 w-6 rounded-full text-destructive hover:text-destructive hover:bg-destructive/10"
          >
            <Trash2 size={14} />
          </Button>
        </div>
      </div>

      <div
        ref={scrollRef}
        className="flex-grow px-1 py-0.5 overflow-y-auto custom-scrollbar font-mono text-xs space-y-0.5"
      >
        {filteredLogs.length === 0 ? (
          <div className="text-gray-500 italic p-1 text-center text-xs">
            No events recorded yet.
          </div>
        ) : (
          filteredLogs.map((log) => {
            const colors = logColors[log.type] || logColors.default;
            const time = formatTime(log.timestamp);
            
            return (
              <div 
                key={log.id} 
                className="flex items-center gap-1.5 py-0.5 px-1 hover:bg-white/5 rounded group"
              >
                <div className={`w-1.5 h-1.5 rounded-full ${colors.dot} flex-shrink-0`} />
                <span className="text-gray-400 text-[10px] flex-shrink-0 opacity-70 group-hover:opacity-100">{time}</span>
                <p className={`text-gray-200 text-[11px] ${log.type === 'error' ? '' : 'truncate'}`}>
                  {log.message}
                </p>
              </div>
            );
          })
        )}
      </div>
    </div>
  );
};

export default LogPanel; 